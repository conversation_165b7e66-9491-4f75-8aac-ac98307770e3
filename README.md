# SourceFlex Admin Console (FlexAdmin)

FlexAdmin is the administration panel for the SourceFlex platform, built using Refine, Ant Design, and integrated with Logto for authentication and Hasura for GraphQL data operations.

## Features

- 🔐 Secure authentication with Logto
- 📊 Comprehensive admin interface using Refine framework
- 🎨 Beautiful UI with Ant Design components
- 🔄 Real-time data with Hasura GraphQL
- 👥 Multi-tenant organization support
- 🔒 Role-based access control

## Tech Stack

- **Frontend Framework**: React 18
- **Admin Framework**: Refine
- **UI Components**: Ant Design
- **Authentication**: Logto
- **GraphQL Client**: URQL
- **Real-time Alerts**: tRPC
- **GraphQL Code Generation**: GraphQL Codegen

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Docker and Docker Compose (for local development environment)
- Logto, Postgres, Hasura, and DocuSeal services running

### Installation

1. Clone the repository:
```bash
git clone [repository-url]
cd FlexAdmin
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables by copying `.env.example` to `.env` and filling in the values:
```bash
cp .env.example .env
```

4. Start the development server:
```bash
npm run dev
```

### Docker Setup

The application is designed to work with Docker services. Make sure you have the following services running:

```bash
docker-compose up -d
```

This will start Logto, Postgres, Hasura, and DocuSeal services.

## Authentication Setup

The application uses Logto for authentication. See [AUTH_SETUP.md](./docs/AUTH_SETUP.md) for detailed instructions on setting up authentication.

## Project Structure

```
/src
├── /auth           # Authentication related code
├── /components     # Reusable UI components
├── /config         # Configuration files
├── /layouts        # Page layouts
├── /pages          # Application pages
├── /providers      # Auth and data providers
└── /services       # API service implementations
```

## Development

### Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run preview` - Preview the production build

### Adding New Resources

To add a new resource to the admin panel:

1. Define the resource in the `App.tsx` file:
```tsx
resources={[
  {
    name: "users",
    list: "/users",
    create: "/users/create",
    edit: "/users/edit/:id",
    show: "/users/show/:id",
    meta: {
      canDelete: true,
    },
  },
  // Add more resources here
]}
```

2. Create the corresponding pages in the `/pages` directory
3. Update GraphQL queries and mutations as needed

## Deployment

The application will be deployed as Cloudflare Workers with the subdomain `console.sourceflex.io`.

## Documentation

Additional documentation can be found in the `/docs` directory:

- [Authentication Setup](./docs/AUTH_SETUP.md)
- [API Integration](./docs/API_INTEGRATION.md) (to be created)
- [Deployment Guide](./docs/DEPLOYMENT_GUIDE.md) (to be created)
