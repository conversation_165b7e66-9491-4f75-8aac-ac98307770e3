import { AuthBindings } from "@refinedev/core";
import { useLogto } from "@logto/react";
import LogtoClient from "@logto/browser";
import { logtoSpaConfig } from "../config";

// Create the Logto client for user authentication (SPA)
export const createLogtoClient = () => {
  return new LogtoClient({
    endpoint: logtoSpaConfig.endpoint,
    appId: logtoSpaConfig.appId,
    scopes: logtoSpaConfig.scopes,
  });
};

/**
 * The authentication provider for Refine with Logto integration
 * Handles login, logout, check auth status, and user identity
 */
export const authProvider: AuthBindings = {
  // Login with Logto
  login: async () => {
    try {
      const logtoClient = createLogtoClient();
      
      // Redirect to Logto login page
      const redirectUri = window.location.origin + "/callback";
      await logtoClient.signIn(redirectUri);
      
      return {
        success: true,
        redirectTo: "/",
      };
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        error: {
          message: "Lo<PERSON> failed",
          name: "<PERSON>gin Error",
        },
      };
    }
  },

  // Check auth status
  check: async () => {
    try {
      const logtoClient = createLogtoClient();
      const isAuthenticated = await logtoClient.isAuthenticated();
      
      if (isAuthenticated) {
        return {
          authenticated: true,
        };
      }
      
      return {
        authenticated: false,
        redirectTo: "/login",
        error: {
          message: "Please login to continue",
          name: "Not authenticated",
        },
      };
    } catch (error) {
      return {
        authenticated: false,
        redirectTo: "/login",
        error: {
          message: "Failed to check authentication status",
          name: "Auth Check Error",
        },
      };
    }
  },

  // Get user identity from Logto
  getIdentity: async () => {
    try {
      const logtoClient = createLogtoClient();
      const userInfo = await logtoClient.fetchUserInfo();
      
      if (!userInfo) {
        throw new Error("User info not available");
      }
      
      return {
        id: userInfo.sub,
        name: userInfo.name || userInfo.username || userInfo.email || "Admin User",
        email: userInfo.email,
        avatar: userInfo.picture,
        roles: userInfo.roles || [],
        organizations: userInfo.organizations || [],
      };
    } catch (error) {
      console.error("Failed to get user identity:", error);
      return null;
    }
  },

  // Logout from Logto
  logout: async () => {
    try {
      const logtoClient = createLogtoClient();
      const redirectUri = window.location.origin + "/login";
      await logtoClient.signOut(redirectUri);
      
      return {
        success: true,
        redirectTo: "/login",
      };
    } catch (error) {
      console.error("Logout error:", error);
      return {
        success: false,
        error: {
          message: "Logout failed",
          name: "Logout Error",
        },
      };
    }
  },

  // We don't need this for Logto
  onError: async (error) => {
    console.error("Auth provider error:", error);
    const status = error?.status;
    
    if (status === 401 || status === 403) {
      return {
        logout: true,
        redirectTo: "/login",
        error,
      };
    }
    
    return { error };
  },

  // Get permissions from Logto user roles
  getPermissions: async () => {
    try {
      const logtoClient = createLogtoClient();
      const userInfo = await logtoClient.fetchUserInfo();
      
      // Extract roles from the user info
      const roles = userInfo?.roles || [];
      const organizations = userInfo?.organizations || [];
      
      return { roles, organizations };
    } catch (error) {
      console.error("Failed to get permissions:", error);
      return null;
    }
  },
};