import { Client, cacheExchange, fetchExchange } from "@urql/core";
import { DataProvider } from "@refinedev/core";
import { authExchange } from "@urql/exchange-auth";
import { createLogtoClient } from "./authProvider";
import { hasuraConfig } from "../config";

/**
 * Creates a GraphQL client for Hasura with authentication
 */
export const createGraphQLClient = async () => {
  const logtoClient = createLogtoClient();
  
  return new Client({
    url: hasuraConfig.endpoint,
    exchanges: [
      cacheExchange,
      authExchange({
        // Get the auth state from Logto
        getAuth: async ({ authState }) => {
          if (authState) return authState;
          
          try {
            if (await logtoClient.isAuthenticated()) {
              const accessToken = await logtoClient.getAccessToken();
              
              if (accessToken) {
                return { token: accessToken };
              }
            }
            return null;
          } catch (error) {
            console.error("Failed to get access token:", error);
            return null;
          }
        },
        // Add the token to the request
        addAuthToOperation: ({ authState, operation }) => {
          if (!authState || !authState.token) {
            return operation;
          }

          const fetchOptions =
            typeof operation.context.fetchOptions === "function"
              ? operation.context.fetchOptions()
              : operation.context.fetchOptions || {};

          return {
            ...operation,
            context: {
              ...operation.context,
              fetchOptions: {
                ...fetchOptions,
                headers: {
                  ...fetchOptions.headers,
                  Authorization: `Bearer ${authState.token}`,
                },
              },
            },
          };
        },
        didAuthError: ({ error }) => {
          return error.graphQLErrors.some(
            (e) => e.extensions?.code === "FORBIDDEN" || e.extensions?.code === "UNAUTHORIZED"
          );
        },
      }),
      fetchExchange,
    ],
  });
};

/**
 * GraphQL data provider for Refine
 */
export const dataProvider = async (): Promise<DataProvider> => {
  const client = await createGraphQLClient();
  
  return {
    // Get a list of resources
    getList: async ({ resource, pagination, filters, sorters, meta }) => {
      const { current = 1, pageSize = 10 } = pagination || {};
      const offset = (current - 1) * pageSize;
      
      // Build query variables
      const variables: Record<string, any> = {
        limit: pageSize,
        offset,
      };
      
      // Add filters
      if (filters && filters.length > 0) {
        const filterConditions: Record<string, any> = {};
        
        filters.forEach((filter) => {
          if (filter.operator === "eq") {
            filterConditions[filter.field] = { _eq: filter.value };
          } else if (filter.operator === "contains") {
            filterConditions[filter.field] = { _ilike: `%${filter.value}%` };
          }
          // Add more filter operators as needed
        });
        
        variables.where = filterConditions;
      }
      
      // Add sorters
      if (sorters && sorters.length > 0) {
        variables.orderBy = sorters.map((sorter) => ({
          [sorter.field]: sorter.order === "asc" ? "asc" : "desc",
        }));
      }
      
      // Build the GraphQL query
      const query = `
        query Get${resource}($limit: Int!, $offset: Int!, $where: ${resource}_bool_exp, $orderBy: [${resource}_order_by!]) {
          ${resource}(limit: $limit, offset: $offset, where: $where, order_by: $orderBy) {
            ${meta?.fields?.join("\n") || "*"}
          }
          ${resource}_aggregate(where: $where) {
            aggregate {
              count
            }
          }
        }
      `;
      
      const result = await client.query(query, variables).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[resource],
        total: result.data[`${resource}_aggregate`].aggregate.count,
      };
    },
    
    // Implement other required methods for DataProvider
    getOne: async ({ resource, id, meta }) => {
      const query = `
        query Get${resource}ById($id: ID!) {
          ${resource}_by_pk(id: $id) {
            ${meta?.fields?.join("\n") || "*"}
          }
        }
      `;
      
      const result = await client.query(query, { id }).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[`${resource}_by_pk`],
      };
    },
    
    create: async ({ resource, variables, meta }) => {
      const query = `
        mutation Create${resource}($input: ${resource}_insert_input!) {
          insert_${resource}_one(object: $input) {
            ${meta?.fields?.join("\n") || "id"}
          }
        }
      `;
      
      const result = await client.mutation(query, { input: variables }).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[`insert_${resource}_one`],
      };
    },
    
    update: async ({ resource, id, variables, meta }) => {
      const query = `
        mutation Update${resource}($id: ID!, $input: ${resource}_set_input!) {
          update_${resource}_by_pk(pk_columns: { id: $id }, _set: $input) {
            ${meta?.fields?.join("\n") || "id"}
          }
        }
      `;
      
      const result = await client.mutation(query, { id, input: variables }).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[`update_${resource}_by_pk`],
      };
    },
    
    deleteOne: async ({ resource, id, meta }) => {
      const query = `
        mutation Delete${resource}($id: ID!) {
          delete_${resource}_by_pk(id: $id) {
            ${meta?.fields?.join("\n") || "id"}
          }
        }
      `;
      
      const result = await client.mutation(query, { id }).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[`delete_${resource}_by_pk`],
      };
    },
    
    // Implement any custom methods you need
    custom: async ({ url, method, filters, sorters, payload, query, headers, meta }) => {
      console.log("Custom GraphQL operation", { url, method, meta });
      
      let operation;
      let variables = {};
      
      if (meta?.operation) {
        operation = meta.operation;
      } else {
        throw new Error("Custom operation requires meta.operation");
      }
      
      if (meta?.variables) {
        variables = meta.variables;
      }
      
      if (method === "get") {
        const result = await client.query(operation, variables).toPromise();
        
        if (result.error) {
          throw new Error(result.error.message);
        }
        
        return {
          data: result.data,
        };
      } else {
        const result = await client.mutation(operation, variables).toPromise();
        
        if (result.error) {
          throw new Error(result.error.message);
        }
        
        return {
          data: result.data,
        };
      }
    },
    
    // Additional required methods
    getApiUrl: () => {
      return hasuraConfig.endpoint;
    },
    
    getMany: async ({ resource, ids, meta }) => {
      const query = `
        query Get${resource}ByIds($ids: [ID!]!) {
          ${resource}(where: {id: {_in: $ids}}) {
            ${meta?.fields?.join("\n") || "*"}
          }
        }
      `;
      
      const result = await client.query(query, { ids }).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[resource],
      };
    },
    
    createMany: async ({ resource, variables, meta }) => {
      const query = `
        mutation Create${resource}Many($objects: [${resource}_insert_input!]!) {
          insert_${resource}(objects: $objects) {
            returning {
              ${meta?.fields?.join("\n") || "id"}
            }
          }
        }
      `;
      
      const result = await client.mutation(query, { objects: variables }).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[`insert_${resource}`].returning,
      };
    },
    
    deleteMany: async ({ resource, ids, meta }) => {
      const query = `
        mutation Delete${resource}Many($ids: [ID!]!) {
          delete_${resource}(where: {id: {_in: $ids}}) {
            returning {
              ${meta?.fields?.join("\n") || "id"}
            }
          }
        }
      `;
      
      const result = await client.mutation(query, { ids }).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[`delete_${resource}`].returning,
      };
    },
    
    updateMany: async ({ resource, ids, variables, meta }) => {
      const query = `
        mutation Update${resource}Many($ids: [ID!]!, $input: ${resource}_set_input!) {
          update_${resource}(where: {id: {_in: $ids}}, _set: $input) {
            returning {
              ${meta?.fields?.join("\n") || "id"}
            }
          }
        }
      `;
      
      const result = await client.mutation(query, { ids, input: variables }).toPromise();
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        data: result.data[`update_${resource}`].returning,
      };
    },
  };
};
