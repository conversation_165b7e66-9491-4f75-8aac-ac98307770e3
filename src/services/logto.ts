import { logtoM2MConfig } from '../config';

interface M2MTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

/**
 * Service for M2M (Machine-to-Machine) authentication with Logto
 * This handles backend operations through the Management API
 */
class LogtoM2MService {
  private accessToken: string | null = null;
  private tokenExpiry: number | null = null;

  private async getAccessToken(): Promise<string> {
    // Check if token is still valid (with 5-minute buffer)
    if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry - 300000) {
      console.log('✅ Using cached token');
      return this.accessToken;
    }

    console.log('🔄 Getting new access token...');
    console.log('📋 Config check:', {
      endpoint: logtoM2MConfig.endpoint,
      appId: logtoM2MConfig.appId,
      appSecret: logtoM2MConfig.appSecret ? '***' + logtoM2MConfig.appSecret.slice(-4) : 'MISSING',
      tokenEndpoint: logtoM2MConfig.tokenEndpoint,
      managementApi: logtoM2MConfig.managementApi
    });

    // Get new token
    const credentials = btoa(`${logtoM2MConfig.appId}:${logtoM2MConfig.appSecret}`);
    
    console.log('🔑 Making token request to:', logtoM2MConfig.tokenEndpoint);
    
    try {
      const response = await fetch(logtoM2MConfig.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          resource: logtoM2MConfig.resource,
          scope: 'all'
        }),
        // Don't include credentials for M2M requests
        // credentials: 'include',
        mode: 'cors',
      });

      console.log('📡 Token response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Token request failed:', errorText);
        throw new Error(`Failed to get access token: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const tokenData: M2MTokenResponse = await response.json();
      console.log('✅ Token received, expires in:', tokenData.expires_in, 'seconds');
      
      this.accessToken = tokenData.access_token;
      this.tokenExpiry = Date.now() + (tokenData.expires_in * 1000);
      
      return this.accessToken;
    } catch (error) {
      console.error('❌ Token request error:', error);
      throw error;
    }
  }

  async callManagementApi<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    try {
      const token = await this.getAccessToken();
      
      const response = await fetch(`${logtoM2MConfig.managementApi}${endpoint}`, {
        ...options,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          ...options.headers,
        },
        // Don't include credentials for M2M requests
        // credentials: 'include',
        mode: 'cors',
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Management API error:', errorText);
        throw new Error(`Management API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return response.json();
    } catch (error) {
      console.error('❌ Management API call error:', error);
      throw error;
    }
  }

  // Common Management API methods
  async getUsers() {
    return this.callManagementApi('/users');
  }

  async getOrganizations() {
    return this.callManagementApi('/organizations');
  }

  async getApplications() {
    return this.callManagementApi('/applications');
  }

  async getRoles() {
    return this.callManagementApi('/roles');
  }

  async getAuditLogs() {
    return this.callManagementApi('/logs');
  }
}

export const logtoM2MService = new LogtoM2MService();