/**
 * Logto authentication configuration
 * 
 * This file contains the configuration for Logto authentication in the FlexAdmin application.
 * It follows the multi-tenant architecture with:
 * - SPA application for user interface
 * - M2M application for backend operations
 */

// SPA application configuration for user authentication
export const logtoSpaConfig = {
  endpoint: import.meta.env.VITE_LOGTO_ENDPOINT,
  appId: import.meta.env.VITE_LOGTO_SPA_APP_ID,
  scopes: ["profile", "email", "organizations", "organization_roles"],
} as const;

// M2M application configuration for management API access
export const logtoM2MConfig = {
  endpoint: import.meta.env.VITE_LOGTO_ENDPOINT,
  appId: import.meta.env.VITE_LOGTO_APP_ID,
  appSecret: import.meta.env.VITE_LOGTO_APP_SECRET,
  managementApi: import.meta.env.VITE_LOGTO_MANAGEMENT_API,
  tokenEndpoint: import.meta.env.VITE_LOGTO_TOKEN_ENDPOINT,
  resource: import.meta.env.VITE_LOGTO_RESOURCE || "https://default.logto.app/api", // Correct resource identifier
} as const;

// Hasura GraphQL configuration
export const hasuraConfig = {
  endpoint: import.meta.env.VITE_HASURA_ENDPOINT,
} as const;

// Application configuration
export const appConfig = {
  name: import.meta.env.VITE_APP_NAME,
  description: import.meta.env.VITE_APP_DESCRIPTION,
  port: import.meta.env.VITE_PORT,
} as const;