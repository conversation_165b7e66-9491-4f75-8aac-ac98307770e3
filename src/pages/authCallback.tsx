import React, { useEffect, useState } from "react";
import { useLogin, useNavigation } from "@refinedev/core";
import { Layout, Spin, Row, Col, Typography, theme, Alert } from "antd";
import { createLogtoClient } from "../providers/authProvider";

const { Title, Text } = Typography;
const { useToken } = theme;

/**
 * Callback page for handling auth redirect from Logto
 */
export const AuthCallbackPage: React.FC = () => {
  const { token } = useToken();
  const { mutate: login } = useLogin();
  const { replace } = useNavigation();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log("🔄 Handling Logto callback...");
        console.log("📍 Current URL:", window.location.href);
        
        const logtoClient = createLogtoClient();
        
        // Handle the callback from Logto
        await logtoClient.handleSignInCallback();
        console.log("✅ Logto callback handled successfully");
        
        // Check if authentication was successful
        const isAuthenticated = await logtoClient.isAuthenticated();
        console.log("🔑 Authentication status:", isAuthenticated);
        
        if (isAuthenticated) {
          // Successfully logged in, redirect to the dashboard
          console.log("🚀 Redirecting to dashboard...");
          login({});
        } else {
          console.error("❌ Authentication failed after callback");
          setError("Authentication failed after callback. Please try again.");
          setTimeout(() => replace("/login"), 3000);
        }
      } catch (error) {
        console.error("❌ Auth callback error:", error);
        setError(error instanceof Error ? error.message : String(error));
        // Redirect to the login page on error after showing the error
        setTimeout(() => replace("/login"), 3000);
      }
    };

    handleCallback();
  }, [login, replace]);

  return (
    <Layout
      style={{
        height: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: token.colorBgLayout,
      }}
    >
      <Row justify="center" align="middle">
        <Col>
          <div style={{ textAlign: "center" }}>
            {error ? (
              <Alert
                message="Authentication Error"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 16, minWidth: 300 }}
              />
            ) : (
              <>
                <Spin size="large" />
                <Title level={4} style={{ marginTop: "24px" }}>
                  Completing sign-in...
                </Title>
                <Text type="secondary">
                  Please wait while we authenticate you
                </Text>
              </>
            )}
          </div>
        </Col>
      </Row>
    </Layout>
  );
};
