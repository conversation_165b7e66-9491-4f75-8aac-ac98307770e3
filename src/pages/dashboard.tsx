import React from "react";
import { Card, Row, Col, Typography } from "antd";
import { LogtoTestComponent } from "../components/LogtoTestComponent";
import { LogtoDebugger } from "../components/LogtoDebugger";

const { Title } = Typography;

/**
 * Dashboard page component for FlexAdmin
 */
export const DashboardPage: React.FC = () => {
  return (
    <div style={{ padding: "20px" }}>
      <Title level={2}>FlexAdmin Dashboard</Title>
      
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <LogtoDebugger />
        </Col>
        <Col span={24}>
          <LogtoTestComponent />
        </Col>
      </Row>
    </div>
  );
};
