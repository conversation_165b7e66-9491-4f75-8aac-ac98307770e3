import React from "react";
import { useLogin, useTranslate } from "@refinedev/core";
import { <PERSON>ton, Card, Layout, Typography, Row, Col, theme } from "antd";
import { appConfig } from "../config";

const { Title, Text } = Typography;
const { useToken } = theme;

/**
 * Login page component for FlexAdmin
 */
export const LoginPage: React.FC = () => {
  const { token } = useToken();
  const translate = useTranslate();
  const { mutate: login } = useLogin();

  const handleLogin = () => {
    login({});
  };

  return (
    <Layout
      style={{
        height: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: token.colorBgLayout,
      }}
    >
      <Row justify="center" align="middle">
        <Col xs={22} sm={18} md={12} lg={8} xl={6}>
          <Card
            style={{
              boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08)",
              borderRadius: token.borderRadius,
            }}
          >
            <div style={{ textAlign: "center", padding: "16px 0" }}>
              <Title level={3} style={{ color: token.colorPrimary }}>
                {appConfig.name}
              </Title>
              <Text type="secondary">{appConfig.description}</Text>
            </div>

            <div style={{ marginTop: "24px" }}>
              <Button
                type="primary"
                size="large"
                block
                onClick={() => handleLogin()}
                style={{
                  height: "50px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {translate("pages.login.signin", "Sign in with Logto")}
              </Button>
            </div>
          </Card>
        </Col>
      </Row>
    </Layout>
  );
};
