import React, { useEffect, useState } from "react";
import { Card, Typography, Space, Divider, Tag, Button } from "antd";
import { createLogtoClient } from "../providers/authProvider";
import { logtoSpaConfig, logtoM2MConfig } from "../config";

const { Title, Text, Paragraph } = Typography;

/**
 * A debugging component for Logto authentication
 * Shows configuration and current authentication state
 */
export const LogtoDebugger: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const logtoClient = createLogtoClient();
        const authStatus = await logtoClient.isAuthenticated();
        setIsAuthenticated(authStatus);
        
        if (authStatus) {
          try {
            const user = await logtoClient.fetchUserInfo();
            setUserInfo(user);
          } catch (userError) {
            console.error("Failed to fetch user info:", userError);
          }
        }
      } catch (err) {
        console.error("Auth check error:", err);
        setError(err instanceof Error ? err.message : String(err));
      }
    };
    
    checkAuthStatus();
  }, []);

  const handleLogin = async () => {
    try {
      const logtoClient = createLogtoClient();
      const redirectUri = window.location.origin + "/callback";
      await logtoClient.signIn(redirectUri);
    } catch (err) {
      console.error("Login error:", err);
      setError(err instanceof Error ? err.message : String(err));
    }
  };

  const handleLogout = async () => {
    try {
      const logtoClient = createLogtoClient();
      const redirectUri = window.location.origin + "/login";
      await logtoClient.signOut(redirectUri);
    } catch (err) {
      console.error("Logout error:", err);
      setError(err instanceof Error ? err.message : String(err));
    }
  };
  
  return (
    <Card title="Logto Debugger" style={{ marginBottom: 16 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Title level={5}>Environment Configuration</Title>
        <Paragraph>
          <Text strong>SPA App ID:</Text> {logtoSpaConfig.appId || 'Not set'}
        </Paragraph>
        <Paragraph>
          <Text strong>Logto Endpoint:</Text> {logtoSpaConfig.endpoint || 'Not set'}
        </Paragraph>
        <Paragraph>
          <Text strong>Scopes:</Text> {logtoSpaConfig.scopes?.join(', ') || 'Not set'}
        </Paragraph>
        
        <Divider />
        
        <Title level={5}>Authentication Status</Title>
        <Paragraph>
          <Text strong>Status:</Text>{' '}
          {isAuthenticated === null ? (
            <Tag color="processing">Checking...</Tag>
          ) : isAuthenticated ? (
            <Tag color="success">Authenticated</Tag>
          ) : (
            <Tag color="error">Not Authenticated</Tag>
          )}
        </Paragraph>
        
        {userInfo && (
          <>
            <Divider />
            <Title level={5}>User Information</Title>
            <Paragraph>
              <Text strong>User ID:</Text> {userInfo.sub}
            </Paragraph>
            <Paragraph>
              <Text strong>Name:</Text> {userInfo.name || 'Not available'}
            </Paragraph>
            <Paragraph>
              <Text strong>Email:</Text> {userInfo.email || 'Not available'}
            </Paragraph>
          </>
        )}
        
        {error && (
          <>
            <Divider />
            <Title level={5}>Error</Title>
            <Paragraph>
              <Text type="danger">{error}</Text>
            </Paragraph>
          </>
        )}
        
        <Divider />
        
        <Space>
          <Button type="primary" onClick={handleLogin}>
            Sign In
          </Button>
          <Button danger onClick={handleLogout}>
            Sign Out
          </Button>
        </Space>
      </Space>
    </Card>
  );
};