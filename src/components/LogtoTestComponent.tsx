import React, { useEffect, useState } from "react";
import { Card, Typography, Spin, Alert, Space } from "antd";
import { logtoM2MService } from "../services/logto";

const { Title, Text } = Typography;

/**
 * Component to test Logto integration
 */
export const LogtoTestComponent: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);

  useEffect(() => {
    const testLogtoConnection = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Test the M2M connection to Logto
        const [users, organizations, applications] = await Promise.all([
          logtoM2MService.getUsers(),
          logtoM2MService.getOrganizations(),
          logtoM2MService.getApplications(),
        ]);
        
        setData({
          users: Array.isArray(users) ? users.length : 0,
          organizations: Array.isArray(organizations) ? organizations.length : 0,
          applications: Array.isArray(applications) ? applications.length : 0,
        });
        
      } catch (err) {
        console.error("Logto connection test failed:", err);
        setError(err instanceof Error ? err.message : String(err));
      } finally {
        setLoading(false);
      }
    };

    testLogtoConnection();
  }, []);

  return (
    <Card title="Logto Connection Test" style={{ maxWidth: 600, margin: '0 auto' }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin tip="Testing Logto connection..." />
        </div>
      ) : error ? (
        <Alert
          type="error"
          message="Connection Failed"
          description={
            <Space direction="vertical">
              <Text>Could not connect to Logto Management API.</Text>
              <Text code>{error}</Text>
              <Text>
                Please check your Logto configuration and make sure the server is running.
                Also verify that CORS is properly configured.
              </Text>
            </Space>
          }
        />
      ) : (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            type="success"
            message="Connection Successful"
            description="Successfully connected to Logto Management API"
          />
          
          <Title level={4}>Statistics</Title>
          <ul>
            <li><Text strong>Users:</Text> {data.users}</li>
            <li><Text strong>Organizations:</Text> {data.organizations}</li>
            <li><Text strong>Applications:</Text> {data.applications}</li>
          </ul>
        </Space>
      )}
    </Card>
  );
};
