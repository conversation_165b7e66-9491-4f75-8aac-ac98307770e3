import React, { useEffect, useState } from "react";
import { Refine } from "@refinedev/core";
import { RefineThemes, ThemedLayoutV2, notificationProvider } from "@refinedev/antd";
import routerProvider, { NavigateToResource } from "@refinedev/react-router-v6";
import { BrowserRouter, Routes, Route, Outlet } from "react-router-dom";
import { ConfigProvider, App as AntdApp, Spin } from "antd";
import "@refinedev/antd/dist/reset.css";

// Providers
import { authProvider } from "./providers/authProvider";
import { dataProvider as createDataProvider } from "./providers/dataProvider";

// Pages
import { LoginPage } from "./pages/login";
import { AuthCallbackPage } from "./pages/authCallback";
import { DashboardPage } from "./pages/dashboard";
import { ErrorComponent } from "@refinedev/antd";

const App: React.FC = () => {
  const [dataProvider, setDataProvider] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        const provider = await createDataProvider();
        setDataProvider(provider);
      } catch (error) {
        console.error("Failed to initialize data provider:", error);
      } finally {
        setLoading(false);
      }
    };

    initializeApp();
  }, []);

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="Loading FlexAdmin..." />
      </div>
    );
  }

  return (
    <BrowserRouter>
      <ConfigProvider theme={RefineThemes.Blue}>
        <AntdApp>
          <Refine
            authProvider={authProvider}
            dataProvider={{ default: dataProvider }}
            routerProvider={routerProvider}
            notificationProvider={notificationProvider}
            resources={[
              // Define your resources here
              {
                name: "dashboard",
                list: "/dashboard",
                meta: {
                  label: "Dashboard",
                  icon: <span>📊</span>,
                },
              },
              // Example:
              // {
              //   name: "users",
              //   list: "/users",
              //   create: "/users/create",
              //   edit: "/users/edit/:id",
              //   show: "/users/show/:id",
              //   meta: {
              //     canDelete: true,
              //     icon: <span>👥</span>,
              //   },
              // },
            ]}
            options={{
              syncWithLocation: true,
              warnWhenUnsavedChanges: true,
              projectId: "flexadmin",
            }}
          >
            <Routes>
              <Route
                element={
                  <ThemedLayoutV2>
                    <Outlet />
                  </ThemedLayoutV2>
                }
              >
                {/* Add your protected routes here */}
                <Route index element={<NavigateToResource resource="dashboard" />} />
                
                {/* Dashboard page */}
                <Route path="/dashboard" element={<DashboardPage />} />
              </Route>
              
              <Route path="/login" element={<LoginPage />} />
              <Route path="/callback" element={<AuthCallbackPage />} />
              <Route path="*" element={<ErrorComponent />} />
            </Routes>
          </Refine>
        </AntdApp>
      </ConfigProvider>
    </BrowserRouter>
  );
};

export default App;