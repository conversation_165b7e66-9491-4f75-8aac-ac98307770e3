import { logtoM2MService } from './services/logto';

// Test M2M connection
async function testConnection() {
  try {
    console.log('🔄 Testing Logto M2M connection...');
    
    // Test Management API access
    const users = await logtoM2MService.getUsers();
    console.log('✅ Users fetched:', users.length || 0, 'users');
    
    const organizations = await logtoM2MService.getOrganizations();
    console.log('✅ Organizations fetched:', organizations.length || 0, 'organizations');
    
    const applications = await logtoM2MService.getApplications();
    console.log('✅ Applications fetched:', applications.length || 0, 'applications');
    
    console.log('🎉 M2M connection working perfectly!');
    return true;
  } catch (error) {
    console.error('❌ M2M connection failed:', error);
    return false;
  }
}

// Run test when component mounts
testConnection();
