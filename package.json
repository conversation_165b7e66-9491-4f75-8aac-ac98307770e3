{"name": "flexadmin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "kill -9 $(lsof -t -i:5174) 2>/dev/null || true && vite --port 5174", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@logto/browser": "^2.2.3", "@logto/react": "^4.0.9", "@refinedev/antd": "^5.46.2", "@refinedev/core": "^4.57.10", "@refinedev/react-router-v6": "^4.6.2", "@tanstack/react-query": "^5.83.1", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@urql/core": "^5.2.0", "@urql/exchange-auth": "^2.2.1", "antd": "^5.26.7", "graphql": "^16.11.0", "lucide-react": "^0.535.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.7.1", "urql": "^4.2.2", "zod": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.30.1", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-urql": "^4.0.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}