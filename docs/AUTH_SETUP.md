# SourceFlex Admin Console Setup

This document outlines the authentication setup for the SourceFlex Admin Console.

## Overview

The SourceFlex Admin Console is built using:

- [Refine](https://refine.dev/) - A React-based framework for building admin panels
- [Ant Design](https://ant.design/) - UI component library
- [Logto](https://logto.io/) - Authentication service
- [Hasura GraphQL](https://hasura.io/) - GraphQL API for data operations

## Authentication Flow

The authentication flow is based on Logto's OAuth 2.0 / OpenID Connect implementation:

1. User visits the admin console and clicks "Sign in with Logto"
2. They are redirected to the Logto login page
3. After successful authentication, they are redirected back to the `/callback` route
4. The AuthCallbackPage component handles the authentication callback and redirects to the dashboard
5. All subsequent API requests include the access token for authorization

## Key Components

### Auth Provider

The `authProvider.ts` file contains the Refine auth provider implementation that integrates with Logto. It handles:

- Login/logout flows
- Authentication status checks
- User identity fetching
- Permission management

### Data Provider

The `dataProvider.ts` file contains the Refine data provider implementation that integrates with Hasura GraphQL. It:

- Automatically includes the auth token in all requests
- Handles common CRUD operations
- Supports filtering, sorting, and pagination
- Provides custom GraphQL operations when needed

### Components

- **LoginPage**: Simple login page with a "Sign in with Logto" button
- **AuthCallbackPage**: Handles the OAuth callback after successful authentication
- **App**: Main application setup with Refine configuration

## Environment Variables

The following environment variables are required:

```
# Logto Configuration
VITE_LOGTO_ENDPOINT=http://localhost:3001
VITE_LOGTO_APP_ID=<your-app-id>
VITE_LOGTO_APP_SECRET=<your-app-secret>

# API Endpoints
VITE_LOGTO_MANAGEMENT_API=http://localhost:3001/api
VITE_LOGTO_TOKEN_ENDPOINT=http://localhost:3001/oidc/token

# Hasura Configuration
VITE_HASURA_ENDPOINT=http://localhost:8080/v1/graphql

# App Configuration
VITE_APP_NAME=FlexAdmin
VITE_APP_DESCRIPTION=SourceFlex Admin Console
VITE_PORT=5174
```

## Setup Instructions

1. Ensure Logto and Hasura services are running (typically using Docker Compose)
2. Configure the application in the Logto Admin Console:
   - Create a "Traditional Web Application"
   - Set the redirect URI to `http://localhost:5174/callback`
   - Enable the necessary scopes (profile, email, organizations)
   - Copy the App ID and App Secret to your environment variables
3. Run the development server: `npm run dev`

## Security Considerations

- The application uses the authorization code flow with PKCE for authentication
- Access tokens are short-lived and stored in memory only
- All API requests are authenticated using the access token
- Role-based access control is applied at both the UI and API level

## Multi-Tenant Support

The application supports multi-tenant isolation through Logto's organization features:

- Users belong to specific organizations
- Organization roles determine permissions
- Data is filtered based on the user's organization context
- Hasura's row-level security ensures complete tenant isolation
