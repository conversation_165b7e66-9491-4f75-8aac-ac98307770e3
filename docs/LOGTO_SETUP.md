# Logto Dual-Application Setup

This guide explains how to set up the dual-application architecture for Logto integration with FlexAdmin.

## Architecture Overview

FlexAdmin uses a dual-application approach for Logto integration:

1. **SPA Application** - For user authentication and UI interactions
2. **M2M Application** - For management API access and backend operations

This approach follows the recommended pattern for admin panels that need to interact with Logto's Management API.

## Creating the Applications in Logto

### Step 1: Create the M2M Application

1. Log in to Logto Admin Console (http://localhost:3002)
2. Go to "Applications" and click "Create Application"
3. Select "Machine-to-Machine" as the application type
4. Enter the following details:
   - Name: FlexAdmin M2M
   - Description: Machine-to-machine app for FlexAdmin backend operations

5. After creation, make note of:
   - App ID
   - App Secret

6. Assign the "Logto Management API access" role:
   - Go to the "Roles" tab for the M2M application
   - Add the "Logto Management API access" role

### Step 2: Create the SPA Application

1. Go back to "Applications" and click "Create Application"
2. Select "Single Page App" as the application type
3. Enter the following details:
   - Name: FlexAdmin SPA
   - Description: SPA for FlexAdmin user interface

4. Configure redirect URIs:
   - Add: http://localhost:5174/callback

5. After creation, make note of the App ID

6. Create and assign appropriate roles:
   - Go to the "Roles" tab for the SPA application
   - Add any necessary organization roles

## Configuring FlexAdmin

Update your `.env` file with the application details:

```
# Logto Configuration
VITE_LOGTO_ENDPOINT=http://localhost:3001

# SPA Application (for user interface)
VITE_LOGTO_SPA_APP_ID=your_spa_app_id_here

# M2M Application (for backend operations)
VITE_LOGTO_APP_ID=your_m2m_app_id_here
VITE_LOGTO_APP_SECRET=your_m2m_app_secret_here

# API Endpoints
VITE_LOGTO_MANAGEMENT_API=http://localhost:3001/api
VITE_LOGTO_TOKEN_ENDPOINT=http://localhost:3001/oidc/token

# Hasura Configuration
VITE_HASURA_ENDPOINT=http://localhost:8080/v1/graphql
```

## How It Works

### Authentication Flow

1. **User Authentication (SPA):**
   - User clicks "Sign in" in the admin panel
   - They are redirected to Logto's login page
   - After successful authentication, they are redirected back to the callback URL
   - The SPA receives and stores the access token for authenticated user actions

2. **Management API Access (M2M):**
   - Backend operations use the M2M app credentials
   - The application requests a token using client credentials flow
   - This token is used to call the Management API
   - No user interaction is required for this flow

### Code Architecture

The code is structured to support this dual-application approach:

- `src/config/auth.ts` - Contains separate configurations for SPA and M2M
- `src/providers/authProvider.ts` - Handles user authentication with the SPA app
- `src/services/logto.ts` - Manages M2M authentication for Management API access

## Testing the Setup

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Visit http://localhost:5174
3. Test both authentication flows:
   - User login (SPA): Click the login button and authenticate
   - Management API access (M2M): Check the dashboard to see if it can fetch data

## Troubleshooting

If you encounter issues:

- Check that both applications are properly configured in Logto
- Verify that the M2M application has the "Logto Management API access" role
- Ensure all App IDs and secrets are correctly set in your `.env` file
- Check browser console for any CORS or authentication errors

For more detailed troubleshooting, see the [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) guide.
