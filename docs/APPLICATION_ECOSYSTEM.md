# SourceFlex Application Ecosystem

This document provides an overview of the SourceFlex application ecosystem and how the different applications interact with each other.

## Applications

SourceFlex consists of four main applications, each serving a specific purpose:

1. **SourceFlex Landing Page** (`sourceflex.io`)
   - Public-facing marketing website
   - Provides information about SourceFlex services
   - Contains sign-up and login links to the other applications

2. **Business User Application** (`app.sourceflex.io`)
   - Custom React application for business users
   - Handles project management, job postings, candidate reviews
   - Connects to Hasura GraphQL API for data operations

3. **Talent Portal** (`talent.sourceflex.io`)
   - React Native application for candidates/jobseekers
   - Manages profiles, job applications, and communication
   - Also connects to the same Hasura GraphQL backend

4. **Admin Console** (`console.sourceflex.io`)
   - Administrative panel built with Refine and Ant Design
   - Manages users, organizations, and system configurations
   - Used internally by SourceFlex staff for backend operations

## Authentication Architecture

All applications use Logto for authentication, with the following configuration:

### Logto Applications

1. **Business App**
   - Type: Single Page Application
   - Redirect URI: `https://app.sourceflex.io/callback`
   - Scopes: profile, email, organizations, organization_roles

2. **Talent Portal**
   - Type: Native Application
   - Redirect URI: Custom URL scheme for mobile
   - Scopes: profile, email, organizations, organization_roles

3. **Admin Console**
   - Type: Traditional Web Application
   - Redirect URI: `https://console.sourceflex.io/callback`
   - Scopes: profile, email, organizations, organization_roles
   - Access to Management API

4. **M2M Application**
   - Type: Machine-to-Machine
   - Used for server-to-server operations
   - No user interaction required
   - Access to Management API

### Authentication Flow

1. Users log in through Logto's authentication page
2. After successful authentication, they are redirected back to the application
3. Applications exchange the authorization code for access and ID tokens
4. Access tokens are used for API requests to Hasura and other services
5. ID tokens provide user information for the application

## Data Architecture

All applications connect to the same Hasura GraphQL API, which provides:

1. **Multi-tenant isolation**
   - Complete organization isolation at the database level
   - Field restrictions based on user roles
   - Row-level security for data access control

2. **Real-time subscriptions**
   - Live updates for collaborative features
   - Notifications and alerts

3. **Role-based access control**
   - Admin: Full access to all data
   - Recruiter: Project-level access, internal recruitment
   - BenchSales: Bench candidates, external opportunities
   - Candidate: Own profile only, public job applications
   - Member: View-only for assigned records

## Deployment Architecture

All applications are deployed as Cloudflare Workers with their respective subdomains:

1. Landing Page: Cloudflare Pages static site
2. Business App: Cloudflare Workers with React SPA
3. Talent Portal: Native app with Cloudflare Workers API
4. Admin Console: Cloudflare Workers with React SPA

## Integration Points

The applications integrate with the following services:

1. **Logto**: Authentication and user management
2. **Hasura**: GraphQL API for data operations
3. **Postgres**: Database for all application data
4. **DocuSeal**: E-signing service for contracts and agreements
5. **Cloudflare R2**: File storage for resumes and documents

## Local Development Environment

For local development, a Docker Compose setup is provided with:

1. Postgres: Database server
2. Hasura: GraphQL engine
3. Logto: Authentication service
4. DocuSeal: E-signing service

## Next Steps

1. Complete the setup of all applications
2. Configure Hasura permissions based on roles
3. Set up E2E testing across all applications
4. Implement CI/CD pipelines for deployment
