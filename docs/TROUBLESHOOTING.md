# Logto Integration Troubleshooting Guide

This guide helps you solve common issues with Logto authentication in the FlexAdmin application.

## CORS Issues

### Symptom
- Error message: `invalid_request: origin http://localhost:5174 not allowed for client`
- Authentication requests fail with CORS errors
- <PERSON><PERSON><PERSON> shows red CORS errors

### Solution
1. **Add localhost:5174 to CORS allowed origins:**
   - Go to Logto Admin Console (http://localhost:3002)
   - Navigate to Applications > FlexAdmin
   - In Settings tab, find "CORS allowed origins"
   - Add http://localhost:5174 to the list
   - Save changes

2. **Alternative: Use the update script:**
   ```bash
   cd /Users/<USER>/Desktop/SourceFlex/FlexAdmin
   LOGTO_ENDPOINT=http://localhost:3001 LOGTO_APP_ID=b4ow0s1kricuzomofhlpr LOGTO_APP_SECRET=your_secret_here ./scripts/update-logto-settings.sh
   ```

3. **Check Logto CORS configuration:**
   - Make sure <PERSON><PERSON><PERSON>'s Docker container has the correct CORS settings
   - Check your docker-compose.yml file for CORS_ALLOW_ORIGIN environment variable

## Authentication Flow Issues

### Symptom
- "AuthBindings" not found error
- React errors in the console
- Auth redirects not working

### Solution
1. **Check Refine version compatibility:**
   - Make sure you're using the correct imports:
   ```typescript
   import { AuthProvider } from "@refinedev/core";
   ```
   - Not:
   ```typescript
   import { AuthBindings } from "@refinedev/core";
   ```

2. **Verify redirect URIs:**
   - Ensure callback URL is correctly set in Logto
   - The callback URL should be: http://localhost:5174/callback
   - Update your authProvider.ts to use the correct redirect URL

3. **Clear browser cache and storage:**
   - Clear your browser's local storage and cookies
   - Try in an incognito/private window

## M2M Authentication Issues

### Symptom
- Unable to access Management API
- Token endpoint errors
- "Client authentication failed" errors

### Solution
1. **Check application type:**
   - Ensure the application is set as "Machine-to-Machine"
   - Verify that it has the correct roles assigned

2. **Verify credentials:**
   - Double-check the App ID and App Secret in your .env file
   - Make sure they match exactly what's in the Logto console

3. **Check token endpoint:**
   - The token endpoint should be http://localhost:3001/oidc/token
   - Verify the resource parameter is correct: http://localhost:3001/api

4. **Debug with API client:**
   - Test the token endpoint directly with Postman or curl:
   ```bash
   curl -X POST \
     -u "YOUR_APP_ID:YOUR_APP_SECRET" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "grant_type=client_credentials&resource=http://localhost:3001/api&scope=all" \
     http://localhost:3001/oidc/token
   ```

## Hasura Integration Issues

### Symptom
- GraphQL errors
- Unable to fetch data
- Authentication errors with Hasura

### Solution
1. **Check JWT configuration:**
   - Verify that Hasura is configured to use Logto's JWT
   - The JWKS endpoint should be: http://localhost:3001/oidc/jwks

2. **Inspect token claims:**
   - Use jwt.io to decode your token
   - Check for expected claims: roles, permissions, etc.

3. **Test Hasura directly:**
   - Use the Hasura console to test queries
   - Add the Authorization header with your token

## Docker/Network Issues

### Symptom
- Services can't reach each other
- Network-related errors
- "Connection refused" errors

### Solution
1. **Check Docker network:**
   - Ensure all services are on the same Docker network
   - Check that port mappings are correct

2. **Use proper host names:**
   - Inside Docker, use service names instead of localhost
   - Example: http://logto:3001 instead of http://localhost:3001

3. **Verify health checks:**
   - Make sure all services are healthy
   - Check logs for any startup errors

## Role and Permission Issues

### Symptom
- Access denied errors
- Unable to perform actions in the admin console
- "Insufficient permissions" errors

### Solution
1. **Check role assignments:**
   - Verify that your M2M app has the "Logto Management API access" role
   - Add any additional roles as needed

2. **Check API resource configuration:**
   - Verify that the API resource is correctly configured
   - Check permissions assigned to the resource

3. **Review permissions model:**
   - Understand how Logto and Hasura permissions work together
   - Adjust permissions as needed

## Still Having Issues?

If you're still experiencing problems:

1. **Check logs:**
   - Look at Logto container logs
   - Check FlexAdmin console errors
   - Look for any errors in the Hasura logs

2. **Enable verbose logging:**
   - Set `VITE_DEBUG=true` in your .env file
   - Restart the development server

3. **Contact support:**
   - Provide detailed error messages
   - Share logs and configuration
   - Describe the steps to reproduce the issue
