#!/bin/bash

# Script to update Logto application settings
# This script ensures <PERSON><PERSON> is configured properly

echo "🔧 Updating Logto application settings for FlexAdmin..."

# Check if curl is installed
if ! command -v curl &> /dev/null; then
    echo "❌ Error: curl is not installed. Please install curl and try again."
    exit 1
fi

# Check for required env variables
if [ -z "$LOGTO_ENDPOINT" ] || [ -z "$LOGTO_APP_ID" ] || [ -z "$LOGTO_APP_SECRET" ]; then
    # Try to load from .env file
    if [ -f ".env" ]; then
        export $(grep -v '^#' .env | xargs)
    else
        echo "❌ Error: Missing required environment variables. Please set LOGTO_ENDPOINT, LOGTO_APP_ID, and LOGTO_APP_SECRET."
        exit 1
    fi
fi

# Get token for API access
echo "🔑 Getting access token..."
TOKEN_RESPONSE=$(curl -s -X POST \
    -u "$LOGTO_APP_ID:$LOGTO_APP_SECRET" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=client_credentials&resource=http://localhost:3001/api&scope=all" \
    "$LOGTO_ENDPOINT/oidc/token")

ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "❌ Failed to get access token. Response: $TOKEN_RESPONSE"
    exit 1
fi

echo "✅ Successfully obtained access token"

# Update application settings
echo "📝 Updating application CORS settings..."
CORS_RESPONSE=$(curl -s -X PATCH \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "oidcClientMetadata": {
            "corsAllowedOrigins": ["http://localhost:5174"]
        }
    }' \
    "$LOGTO_ENDPOINT/api/applications/$LOGTO_APP_ID")

echo "🔄 Response: $CORS_RESPONSE"

echo "✅ Configuration complete!"
echo ""
echo "🚀 Next steps:"
echo "1. Start the development server: npm run dev"
echo "2. Visit http://localhost:5174 to test the application"
echo ""
echo "If you encounter any CORS issues, make sure Logto is configured to allow requests from http://localhost:5174"
