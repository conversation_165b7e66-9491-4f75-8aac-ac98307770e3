#!/bin/bash

# Script to set up both SPA and M2M applications in Logto
# This script helps create and configure the dual-application architecture

echo "🚀 Setting up Logto dual-application architecture for FlexAdmin..."

# Check if curl is installed
if ! command -v curl &> /dev/null; then
    echo "❌ Error: curl is not installed. Please install curl and try again."
    exit 1
fi

# Read Logto endpoint
read -p "Enter Logto endpoint (default: http://localhost:3001): " LOGTO_ENDPOINT
LOGTO_ENDPOINT=${LOGTO_ENDPOINT:-http://localhost:3001}

# Get admin credentials
read -p "Enter admin username (email): " ADMIN_USERNAME
read -sp "Enter admin password: " ADMIN_PASSWORD
echo ""

# Step 1: Log in to get an admin token
echo "🔑 Logging in to Logto..."
LOGIN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"$ADMIN_USERNAME\",\"password\":\"$ADMIN_PASSWORD\"}" \
    "$LOGTO_ENDPOINT/api/session")

ADMIN_TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

if [ -z "$ADMIN_TOKEN" ]; then
    echo "❌ Failed to log in. Response: $LOGIN_RESPONSE"
    exit 1
fi

echo "✅ Successfully logged in"

# Step 2: Create M2M application
echo "📦 Creating M2M application..."
M2M_RESPONSE=$(curl -s -X POST \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "FlexAdmin M2M",
        "description": "Machine-to-machine app for FlexAdmin backend operations",
        "type": "Machine-to-Machine"
    }' \
    "$LOGTO_ENDPOINT/api/applications")

M2M_APP_ID=$(echo $M2M_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
M2M_APP_SECRET=$(echo $M2M_RESPONSE | grep -o '"secret":"[^"]*' | cut -d'"' -f4)

if [ -z "$M2M_APP_ID" ]; then
    echo "❌ Failed to create M2M application. Response: $M2M_RESPONSE"
    exit 1
fi

echo "✅ Successfully created M2M application"
echo "📝 M2M App ID: $M2M_APP_ID"
echo "🔑 M2M App Secret: $M2M_APP_SECRET"

# Step 3: Create SPA application
echo "🖥️ Creating SPA application..."
SPA_RESPONSE=$(curl -s -X POST \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "FlexAdmin SPA",
        "description": "SPA for FlexAdmin user interface",
        "type": "SPA",
        "oidcClientMetadata": {
            "redirectUris": ["http://localhost:5174/callback"],
            "postLogoutRedirectUris": ["http://localhost:5174/login"]
        }
    }' \
    "$LOGTO_ENDPOINT/api/applications")

SPA_APP_ID=$(echo $SPA_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$SPA_APP_ID" ]; then
    echo "❌ Failed to create SPA application. Response: $SPA_RESPONSE"
    exit 1
fi

echo "✅ Successfully created SPA application"
echo "📝 SPA App ID: $SPA_APP_ID"

# Step 4: Assign Logto Management API access role to M2M app
echo "🔐 Assigning Management API role to M2M application..."

# First, get the Logto Management API role ID
ROLES_RESPONSE=$(curl -s -X GET \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    "$LOGTO_ENDPOINT/api/roles")

MGMT_ROLE_ID=$(echo $ROLES_RESPONSE | grep -o '"id":"[^"]*","name":"Logto Management API access"' | cut -d'"' -f4)

if [ -z "$MGMT_ROLE_ID" ]; then
    echo "❌ Failed to find Management API role. Response: $ROLES_RESPONSE"
    exit 1
fi

# Assign the role to the M2M app
ROLE_ASSIGN_RESPONSE=$(curl -s -X POST \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{\"applicationId\":\"$M2M_APP_ID\"}" \
    "$LOGTO_ENDPOINT/api/roles/$MGMT_ROLE_ID/applications")

echo "✅ Successfully assigned Management API role to M2M application"

# Step 5: Create .env file
echo "📄 Creating .env file..."
cat > .env << EOL
# Logto Configuration
VITE_LOGTO_ENDPOINT=$LOGTO_ENDPOINT

# SPA Application (for user interface)
VITE_LOGTO_SPA_APP_ID=$SPA_APP_ID

# M2M Application (for backend operations)
VITE_LOGTO_APP_ID=$M2M_APP_ID
VITE_LOGTO_APP_SECRET=$M2M_APP_SECRET

# API Endpoints
VITE_LOGTO_MANAGEMENT_API=$LOGTO_ENDPOINT/api
VITE_LOGTO_TOKEN_ENDPOINT=$LOGTO_ENDPOINT/oidc/token

# Hasura Configuration
VITE_HASURA_ENDPOINT=http://localhost:8080/v1/graphql

# App Configuration
VITE_APP_NAME=FlexAdmin
VITE_APP_DESCRIPTION=SourceFlex Admin Console
VITE_PORT=5174
EOL

echo "✅ Successfully created .env file"

echo "🎉 Setup complete!"
echo ""
echo "🚀 Next steps:"
echo "1. Start the development server: npm run dev"
echo "2. Visit http://localhost:5174 to test the application"
echo ""
echo "See the docs/LOGTO_SETUP.md for more information on the dual-application architecture."
